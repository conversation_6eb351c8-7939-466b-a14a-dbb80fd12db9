<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { staffApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Staff | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { patternRules, formRules } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增员工',
    edit: '编辑员工'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Wms.Staff,
  'username' | 'password' | 'gender' | 'avatar' | 'nickname' | 'phone' | 'email' | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    username: '',
    password: '',
    gender: 0,
    avatar: '',
    nickname: '',
    phone: '',
    email: '',
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'username' | 'password' | 'phone' | 'email'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  username: formRules.userName,
  password: [patternRules.pwd],
  phone: [patternRules.phone],
  email: [patternRules.email]
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await staffApi.save(model.value) : await staffApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="登录名" path="username">
          <NInput
            v-model:value="model.username"
            placeholder="请输入登录名"
            :disabled="operateType === 'edit'"
            clearable
          />
        </NFormItem>
        <NFormItem label="密码" path="password">
          <NInput
            v-model:value="model.password"
            type="password"
            placeholder="请输入密码"
            show-password-on="mousedown"
            clearable
          />
        </NFormItem>
        <NFormItem label="昵称" path="nickname">
          <NInput v-model:value="model.nickname" placeholder="请输入昵称" clearable />
        </NFormItem>
        <NFormItem label="性别" path="gender">
          <NRadioGroup v-model:value="model.gender">
            <NSpace>
              <NRadio
                v-for="item in useDict('number').items('Gender')"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NSpace>
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="头像" path="avatar">
          <UploadCover v-model:value="model.avatar" :options="{ maxWidth: 200, maxHeight: 200 }" />
        </NFormItem>
        <NFormItem label="手机号" path="phone">
          <NInput v-model:value="model.phone" placeholder="请输入手机号" clearable />
        </NFormItem>
        <NFormItem label="邮箱" path="email">
          <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
