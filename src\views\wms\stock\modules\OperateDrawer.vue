<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { itemApi, skuApi, stockApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Stock | null;
  /** the meta options */
  metaOptions?: TreeOption[];
  /** the area options */
  areaOptions?: TreeOption[];
  /** the area id */
  areaId?: number | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const unit = ref<string>('');

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增库存',
    edit: '编辑库存'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Wms.Stock, 'metaId' | 'itemId' | 'skuId' | 'areaId' | 'areaPath' | 'num' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    metaId: props.rowData?.item?.metaId || 0,
    itemId: null,
    skuId: null,
    areaId: props.areaId || null,
    areaPath: [],
    num: 0,
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'itemId' | 'skuId' | 'areaId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  itemId: defaultRequiredRule,
  skuId: defaultRequiredRule,
  areaId: defaultRequiredRule
};

async function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);

    if (props.rowData?.areaPath?.length)
      model.value.areaId = props.rowData?.areaPath[props.rowData?.areaPath?.length - 1];
    unit.value = props.rowData?.sku?.unit || '';
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  try {
    loading.value = true;
    await validate();
    // request
    const { error } = props.operateType === 'edit' ? await stockApi.save(model.value) : await stockApi.add(model.value);
    loading.value = false;

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    } else {
      window.$message?.error(`${title.value}失败`);
    }
  } catch (error) {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const itemOptions = ref<TreeOption[]>([]);
const skuOptions = ref<TreeOption[]>([]);

async function getItems() {
  // 获取物料
  const { data, error } = await itemApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    metaId: model.value.metaId
  });
  if (!error) {
    itemOptions.value = data.records;
    skuOptions.value = [];
  }
}

async function getSkus() {
  // 获取规格
  const { data, error } = await skuApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    itemId: model.value.itemId
  });
  if (!error) {
    skuOptions.value = data.records;
  }
}

watch(
  () => model.value.metaId,
  async nval => {
    if (nval) {
      await getItems();
    } else {
      model.value.itemId = null;
      model.value.skuId = null;
      itemOptions.value = [];
      skuOptions.value = [];
    }
  }
);

watch(
  () => model.value.itemId,
  async nval => {
    if (nval) {
      await getSkus();
    } else {
      model.value.skuId = null;
      skuOptions.value = [];
    }
  }
);

function handleSkuChange(value: number, meta: Api.Wms.Sku) {
  unit.value = value ? meta.unit : '';
}

function handleAreaChange(value: number, area: Api.Wms.Area) {
  model.value.areaPath = value ? [...area.parentPath, area.id] : [];
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="分类" path="metaId">
          <NTreeSelect
            v-model:value="model.metaId"
            :options="metaOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择分类"
            clearable
          />
        </NFormItem>
        <NFormItem label="物料" path="itemId">
          <NTreeSelect
            v-model:value="model.itemId"
            :options="itemOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择物料"
            clearable
            :disabled="itemOptions.length === 0"
          />
        </NFormItem>
        <NFormItem label="规格" path="skuId">
          <NTreeSelect
            v-model:value="model.skuId"
            :options="skuOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择规格"
            clearable
            :disabled="skuOptions.length === 0"
            @update:value="handleSkuChange"
          />
        </NFormItem>
        <NFormItem label="库位" path="areaId">
          <NTreeSelect
            v-model:value="model.areaId"
            :options="areaOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择库位"
            clearable
            @update:value="handleAreaChange"
          />
        </NFormItem>
        <NFormItem label="库存量" path="num">
          <NInputNumber v-model:value="model.num" :min="0" :precision="2" placeholder="请输入库存数量" clearable>
            <template #suffix>
              <span>{{ unit }}</span>
            </template>
          </NInputNumber>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
