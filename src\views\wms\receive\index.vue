<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import emitter from '@/utils/mitt';
import Main from './modules/main/index.vue';
// import Sku from './modules/sku/index.vue';

const item = ref<Api.Wms.Item | null>(null);

emitter.on('showSku', (data: Api.Wms.Item | null) => {
  item.value = data;
});

onUnmounted(() => {
  emitter.off('showSku');
});
</script>

<template>
  <div class="relative m-12px h-full">
    <Transition name="fade" mode="out-in">
      <Main v-show="!item" key="main" class="absolute inset-0" />
    </Transition>
    <Transition name="fade" mode="out-in">
      <!-- <Sku v-if="item" class="absolute inset-0" :item="item" key="sku" /> -->
    </Transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
