<script setup lang="ts">
import { ref } from 'vue';
import { compact } from 'lodash-es';

defineOptions({
  name: 'RemoteSelect',
  inheritAttrs: false
});

const props = defineProps<{
  /** the api function */
  apiFn: (params: any) => Promise<any>;
  /** the options */
  options?: any[];
  /** additional filter to merge with api call */
  filter?: Record<string, any>;
  /** the default option */
  defaultOption?: any;
}>();

const value = defineModel<number | string | null | undefined>('value', { required: true });

const loading = ref(false);

const remoteOptions = ref<any[]>(compact(props.options) || [props.defaultOption]);

const onSearch = async (q: string) => {
  loading.value = true;
  try {
    const { data } = await props.apiFn({
      _page: 1,
      _limit: 10,
      status: true,
      q,
      ...props.filter
    });
    remoteOptions.value = data.records;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="remoteOptions"
    :loading="loading"
    v-bind="$attrs"
    remote
    filterable
    clearable
    @search="onSearch"
  />
</template>

<style scoped></style>
