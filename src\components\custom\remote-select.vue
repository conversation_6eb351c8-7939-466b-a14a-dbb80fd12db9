<script setup lang="ts">
import { ref, computed } from 'vue';
import { compact } from 'lodash-es';

defineOptions({
  name: 'RemoteSelect',
  inheritAttrs: false
});

const props = defineProps<{
  /** the api function */
  apiFn: (params: any) => Promise<any>;
  /** the options */
  options?: any[];
  /** additional params to merge with api call */
  params?: Record<string, any>;
}>();

const value = defineModel<number | string | null | undefined>('value', { required: true });

const loading = ref(false);

const remoteOptions = ref<any[]>(compact(props.options) || []);

// 计算显示值：当value为0且在选项中找不到匹配项时，显示空字符串
const displayValue = computed({
  get() {
    // 如果value为0，检查是否在选项中有匹配的项
    if (value.value === 0) {
      const hasMatchingOption = remoteOptions.value.some(option =>
        option.value === 0 || option.value === '0'
      );
      // 如果没有匹配的选项，返回null以显示空字符串
      if (!hasMatchingOption) {
        return null;
      }
    }
    return value.value;
  },
  set(newValue) {
    value.value = newValue;
  }
});

const onSearch = async (q: string) => {
  loading.value = true;
  try {
    const { data } = await props.apiFn({
      _page: 1,
      _limit: 10,
      status: true,
      q,
      ...props.params
    });
    remoteOptions.value = data.records;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <NSelect
    v-model:value="displayValue"
    :options="remoteOptions"
    :loading="loading"
    v-bind="$attrs"
    remote
    filterable
    clearable
    @search="onSearch"
  />
</template>

<style scoped></style>
